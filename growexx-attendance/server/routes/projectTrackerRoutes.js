/**
 * This file is used to projectTracker API's routes.
 * Created by Growexx on 23/11/2023.
 * @name projectTrackerRoute
 */
const router = require('express').Router();
const projectTrackerController = require('../services/getProjectTracker/ProjectTrackerController');
const JiraProjectListController = require('../services/jiraProjectList/jiraProjectListController');
const SprintReportController = require('../services/sprintReport/SprintReportController');
const DownloadProjectSprintReportController = require('../services/DownloadProjectSprintReport/DownloadProjectSprintReportController');
const AuthMiddleWare = require('../middleware/auth');
const ACLMiddleWare = require('../middleware/acl');
const projectTrackerHealthCardController = require('../services/projectTrackerHealthCard/projectTrackerHealthCardController');
const monthlyDeviationController = require('../services/monthlyDeviation/monthlyDeviationController');

router.get('/project/all', AuthMiddleWare, ACLMiddleWare, JiraProjectListController.jiraProjectList);
router.get('/project', AuthMiddleWare, ACLMiddleWare, projectTrackerController.getProjectTracker);
router.get('/epic-names', projectTrackerController.getEpicNames);
router.get('/sprint', AuthMiddleWare, ACLMiddleWare, SprintReportController.getSprintReport);
router.get('/download-report', AuthMiddleWare, ACLMiddleWare, DownloadProjectSprintReportController.downlodProjectSprintReport);
router.get('/health-cards', AuthMiddleWare, ACLMiddleWare, projectTrackerHealthCardController.getProjectTrackerHealthCard);
router.get('/trigger-cron', AuthMiddleWare, ACLMiddleWare, projectTrackerController.triggerCron);
router.get('/cron-status', AuthMiddleWare, ACLMiddleWare, projectTrackerController.getCronStatus);
router.get('/monthly-deviation', AuthMiddleWare, ACLMiddleWare, monthlyDeviationController.getMonthlyDeviation);

module.exports = router;
