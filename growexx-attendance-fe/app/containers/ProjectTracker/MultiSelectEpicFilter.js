/* eslint-disable react/prop-types */
import { Select } from 'antd';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useCallback,
} from 'react';
import { debounce } from 'lodash';
import request from 'utils/request';
import { API_ENDPOINTS } from '../constants';

export default forwardRef((props, ref) => {
  const query = new URLSearchParams(window.location.search);
  const [currentValue, setCurrentValue] = useState(() => {
    const urlValue = query.get(props.field);
    return urlValue ? urlValue.split(',') : [];
  });
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  // Get boardId from URL or props
  const getBoardId = useCallback(() => {
    const urlBoardId = query.get('boardId');
    return urlBoardId || props.boardId || (props.floatingFilterComponentParams && props.floatingFilterComponentParams.boardId);
  }, [props.boardId, props.floatingFilterComponentParams]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (search) => {
      const boardId = getBoardId();
      if (!boardId) return;

      setLoading(true);
      try {
        const URL = `${API_ENDPOINTS.GET_EPIC_NAMES}?boardId=${boardId}&search=${encodeURIComponent(search)}`;
        const response = await request(URL, { method: 'GET' });
        setOptions(response.data || []);
      } catch (error) {
        console.error('Error fetching epic names:', error);
        setOptions([]);
      } finally {
        setLoading(false);
      }
    }, 500),
    [getBoardId]
  );

  // Load initial options
  useEffect(() => {
    debouncedSearch('');
  }, [debouncedSearch]);

  // Handle search input change
  const handleSearch = (value) => {
    setSearchValue(value);
    debouncedSearch(value);
  };

  useImperativeHandle(ref, () => ({
    onParentModelChanged(parentModel) {
      if (!parentModel) {
        setCurrentValue([]);
      } else {
        const filterValue = parentModel.filter;
        setCurrentValue(filterValue ? filterValue.split(',') : []);
      }
    },
  }));

  const onCustomFilterSelect = (values) => {
    setCurrentValue(values || []);

    if (!values || values.length === 0) {
      props.parentFilterInstance(instance => {
        instance.onFloatingFilterChanged(null, null);
      });
    } else {
      const filterValue = values.join(',');
      props.parentFilterInstance(instance => {
        instance.onFloatingFilterChanged('', filterValue);
      });
    }
  };

  const handleDropdownVisibleChange = (open) => {
    if (open) {
      // Reset search when dropdown opens
      setSearchValue('');
      debouncedSearch('');
    }
  };

  return (
    <div>
      <Select
        mode="multiple"
        options={options}
        showSearch
        allowClear
        className="epic-filter"
        onChange={onCustomFilterSelect}
        onSearch={handleSearch}
        onDropdownVisibleChange={handleDropdownVisibleChange}
        filterOption={false} // Disable client-side filtering since we're doing server-side
        loading={loading}
        value={currentValue}
        placeholder="Select epic names..."
        maxTagCount="responsive"
        searchValue={searchValue}
        style={{ minWidth: '200px' }}
      />
    </div>
  );
});
