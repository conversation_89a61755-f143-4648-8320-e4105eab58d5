/* eslint-disable indent */
/* eslint-disable no-underscore-dangle */
import React, { PureComponent } from 'react';
import { Helmet } from 'react-helmet';
import { AgGridReact } from 'ag-grid-react';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';
import XLSX from 'xlsx';
import { Button, PageHeader, Select, Tabs, notification, Switch } from 'antd';
import { get } from 'lodash';
import { FormattedMessage } from 'react-intl';
import request from 'utils/request';
import moment from 'moment';
import { API_ENDPOINTS } from 'containers/constants';
import { InfoCircleOutlined } from '@ant-design/icons';
import {
  getColumnPropsForTrackerSheet,
  getColumnPropsForSprintSheet,
} from './columns';
import { monthlyDeviationColumns } from './monthlyDeviationColumns';
import messages from './messages';
import { StyledProjectTrackerMainDiv } from './StyledProjectTracker';
import StatusCard from '../../components/StatusCard';
import {
  PTA_MOMENT_DATE_FORMAT,
  SPRINT_SHEET_HEADERS,
  SPRINT_SHEET_HEADERS_TO_API_MAPPING,
  TRACKER_SHEET_HEADERS,
  TRACKER_SHEET_HEADERS_TO_API_MAPPING,
} from './constants';
import { setDeepLinkURL } from '../../utils/functions';
import { GET_SORT_ORDER } from '../constants';
import ModalComponent from '../../components/ModalComponent';
import { getInProgressMessage } from './helper';

export class ProjectTracker extends PureComponent {
  constructor(props) {
    super(props);
    const query = new URLSearchParams(window.location.search);
    this.state = {
      showCardsAndTabs: false,
      downloadLoading: false,
      triggerLoading: false,
      isCronRunning: false,
      inProgressProjectName: '',
      epicStatus: 'inProgress',
      // dropdown states
      projectId: query.get('projectId') || '',
      boardId: query.get('boardId') || undefined,
      selectedProjectName: '',
      projectList: [],
      boardList: [],
      projectBoardList: [],
      isLoading: false,

      // status card states
      statusCardsData: {},
      cardsLoading: false,

      // tab states
      sortType: query.get('sortKey') ? Number(query.get('sortType')) : -1,
      sortKey: query.get('createdAt') || 'createdAt',
      sprintList: [],
      projectTracker: {
        list: null,
        columns: getColumnPropsForTrackerSheet([], [], this.showModal, this.state.boardId),
        currentPage: 1,
        filters: {},
        sortType: -1,
        sortKey: 'createdAt',
        showAll: query.get('showAll') !== 'false',
      },
      sprintMetrics: {
        list: null,
        currentPage: 1,
        filters: {},
        sortType: -1,
        sortKey: 'createdAt',
        showAll: query.get('showAll') !== 'false',
      },
      monthlyDeviation: {
        list: null,
        currentPage: 1,
        filters: {},
        sortType: -1,
        sortKey: 'createdAt',
        showAll: query.get('showAll') !== 'false',
      },
      isListLoading: true,
      tab: 'tracker',
      gridApi: null,
      columnApi: null,
      modalContent: null,
      modalVisible: false,
      title: '',
      firstRender: true,
      firstApiCalled: false,
    };
  }

  componentDidMount() {
    const query = new URLSearchParams(window.location.search);
    const projectId = query.get('projectId');
    const boardId = query.get('boardId');
    const queryParamsObj = {};
    if (projectId) {
      queryParamsObj.projectId = projectId;
      queryParamsObj.filterApplied = true;
      this.setState({
        projectId,
      });
      if (boardId) {
        queryParamsObj.boardId = boardId;
        queryParamsObj.projectId = projectId;
        queryParamsObj.filterApplied = true;
        this.setState(
          {
            projectId,
            boardId,
            showCardsAndTabs: true,
          },
          () => {
            this.getCards();
            this.getTabDetails();
          },
        );
      }
    }
    this.checkCronStatus();
    this.getProjectList(queryParamsObj);
  }

  getCards() {
    this.setState({
      cardsLoading: true,
    });
    const { projectId, boardId } = this.state;
    const URL = `${
      API_ENDPOINTS.GET_CARDS
    }?projectId=${projectId}&boardId=${boardId}`;

    request(URL, {
      method: 'GET',
    })
      .then(res => {
        const { data } = res;
        if (
          data.epicEffortsInformation[0].totalSalesEstimates === '0.00' &&
          data.epicEffortsInformation[1].totalSalesEstimates === '0.00'
        ) {
          this.setState({
            epicStatus: 'Done',
          });
        } else {
          this.setState({
            epicStatus: 'InProgress',
          });
        }
        this.setState({
          statusCardsData: data,
        });
      })
      .catch(error => {
        notification.error({
          message: <FormattedMessage {...messages.failedToLoad} />,
          description: error.message,
        });
        this.setState({ isLoading: false });
      })
      .finally(() => {
        this.setState({
          cardsLoading: false,
        });
      });
  }

  getProjectList = ({ projectId: newProjectId, boardId: newBoardId }) => {
    const projectId = newProjectId || this.state.projectId;
    const boardId = newBoardId || this.state.boardId;

    this.setState({ isLoading: true }, () => {
      const URL = API_ENDPOINTS.GET_ALL_PROJECTS;

      request(URL, { method: 'GET' })
        .then(res => {
          const { data } = res;
          let requiredBoard = [];
          let projectName = '';

          if (projectId) {
            const selectedProject = data.find(
              project => project._id === projectId,
            );
            requiredBoard = selectedProject ? selectedProject.boards : [];
            projectName = selectedProject ? selectedProject.projectName : '';
          }

          this.setState(
            {
              isLoading: false,
              projectList: data.map(project => ({
                label: project.projectName,
                value: project._id,
              })),
              boardList: projectId
                ? requiredBoard.map(project => ({
                    label: project.jiraBoardName,
                    value: project._id,
                  }))
                : [],
              boardId: projectId && boardId ? boardId : undefined,
              projectBoardList: data,
              selectedProjectName: projectName,
            },
            () => {
              if (this.state.boardList.length === 1) {
                this.setState(
                  prevState => ({
                    boardId: prevState.boardList[0].value,
                    showCardsAndTabs: true,
                  }),
                  () => {
                    setDeepLinkURL({
                      projectId,
                      boardId: this.state.boardList[0].value,
                      tab: this.state.tab,
                      currentPage: this.state.current,
                      showAll: this.state.projectTracker.showAll,
                      sortKey: this.state.sortKey,
                      sortType: this.state.sortType,
                    });
                    this.getCards();
                    this.getTabDetails();
                  },
                );
              }
            },
          );
        })
        .catch(error => {
          notification.error({
            message: <FormattedMessage {...messages.failedToLoad} />,
            description: error.message,
          });
          this.setState({ isLoading: false });
        });
    });
  };

  showModal = (content, title) => {
    this.setState(prev => ({
      ...prev,
      modalContent: content,
      modalVisible: true,
      title,
    }));
  };

  closeModal = () => {
    this.setState(prev => ({
      ...prev,
      modalContent: null,
      modalVisible: false,
    }));
  };

  filterByProject = value => {
    const query = new URLSearchParams(window.location.search);
    const selectedProjectId = value || '';
    let requiredBoard = [];
    let projectName = '';

    if (selectedProjectId) {
      const selectedProject = this.state.projectBoardList.find(
        project => project._id === selectedProjectId,
      );
      requiredBoard = selectedProject ? selectedProject.boards : [];
      projectName = selectedProject ? selectedProject.projectName : '';
    }
    this.setState(
      {
        projectId: selectedProjectId,
        boardList: selectedProjectId
          ? requiredBoard.map(project => ({
              label: project.jiraBoardName,
              value: project._id,
            }))
          : [],
        boardId: undefined,
        showCardsAndTabs: false,
        selectedProjectName: projectName,
        projectTracker: {
          list: null,
          columns: getColumnPropsForTrackerSheet([], [], this.showModal, this.state.boardId),
          currentPage: 1,
          filters: {},
          sortType: -1,
          sortKey: 'createdAt',
          showAll: query.get('showAll') !== 'false',
        },
        sprintMetrics: {
          list: null,
          currentPage: 1,
          filters: {},
          sortType: -1,
          sortKey: 'createdAt',
          showAll: query.get('showAll') !== 'false',
        },
      },
      () => {
        if (this.state.boardList.length === 1) {
          this.setState(
            prevState => ({
              boardId: prevState.boardList[0].value,
              showCardsAndTabs: true,
            }),
            () => {
              setDeepLinkURL({
                projectId: this.state.projectId,
                boardId: this.state.boardList[0].value,
                tab: this.state.tab,
                currentPage: this.state.current,
                showAll: this.state.projectTracker.showAll,
                sortKey: this.state.sortKey,
                sortType: this.state.sortType,
              });
              this.getCards();
              this.getTabDetails();
            },
          );
        }
      },
    );

    setDeepLinkURL({
      projectId: selectedProjectId,
      boardId: '',
    });
  };

  filterByBoard = value => {
    const query = new URLSearchParams(window.location.search);
    const selectedBoardId = value || '';
    const boardId = selectedBoardId || undefined;
    setDeepLinkURL({
      projectId: this.state.projectId,
      boardId: selectedBoardId || '',
    });
    this.setState(
      {
        boardId,
        showCardsAndTabs: selectedBoardId !== '',
        projectTracker: {
          list: null,
          columns: getColumnPropsForTrackerSheet([], [], this.showModal, this.state.boardId),
          currentPage: 1,
          filters: {},
          sortType: -1,
          sortKey: 'createdAt',
          showAll: query.get('showAll') !== 'false',
        },
        sprintMetrics: {
          list: null,
          currentPage: 1,
          filters: {},
          sortType: -1,
          sortKey: 'createdAt',
          showAll: query.get('showAll') !== 'false',
        },
      },
      () => {
        if (value) {
          this.getCards();
          this.getTabDetails();
        }
      },
    );
  };

  getTabDetails = () => {
    const query = new URLSearchParams(window.location.search);
    const { tab } = this.state;
    const activeTab = query.get('tab') || tab;

    if (activeTab === 'sprints') {
      this.setState({
        sprintMetrics: {
          list: null,
          currentPage: 1,
          filters: {},
          sortType: -1,
          sortKey: 'createdAt',
          showAll: query.get('showAll') !== 'false',
        },
        tab: activeTab,
      });
    } else if (activeTab === 'monthly-deviation') {
      this.setState(
        {
          tab: activeTab,
        },
        () => {
          this.loadMonthlyDeviationDetails();
        },
      );
    } else {
      this.setState({
        projectTracker: {
          list: null,
          columns: getColumnPropsForTrackerSheet([], [], this.showModal, this.state.boardId),
          currentPage: 1,
          filters: {},
          sortType: -1,
          sortKey: 'createdAt',
          showAll: query.get('showAll') !== 'false',
        },
        tab: activeTab,
      });
    }
  };

  loadProjectTrackerDetails = (
    {
      sortType: newSortType,
      sortKey: newSortKey,
      showAll: newShowAll,
      filters: newFilters,
      pagination,
      params,
      prevState,
    } = {},
    updatedKey,
  ) => {
    this.setState({ isListLoading: true });
    const { boardId, projectTracker, sprintMetrics } = this.state;
    let { sortType, sortKey } = this.state;
    sortType = newSortType || sortType;
    sortKey = newSortKey || sortKey;
    let requestURL;
    if (updatedKey === 'sprints') {
      requestURL = `${API_ENDPOINTS.GET_SPRINT_REPORT}?page=${
        pagination.current
      }&limit=${
        pagination.pageSize
      }&boardId=${boardId}&sort=${sortType}&sortBy=${sortKey}`;
      const showAll = newShowAll || sprintMetrics.showAll;
      if (showAll) {
        requestURL += `&isPaginate=1`;
      }
    } else {
      requestURL = `${API_ENDPOINTS.GET_PROJECT_TRACKER}?&page=${
        pagination.current
      }&limit=${
        pagination.pageSize
      }&boardId=${boardId}&sort=${sortType}&sortBy=${sortKey}`;
      const showAll = newShowAll || projectTracker.showAll;
      if (showAll) {
        requestURL += `&isPaginate=1`;
      }
    }

    const filterKeys = Object.keys(newFilters);
    filterKeys.forEach(filter => {
      requestURL += `&${filter}=${newFilters[filter].filter}`;
    });

    request(requestURL, { method: 'GET' })
      .then(response => {
        this.setProjectTrackerDetails(
          response,
          updatedKey,
          params,
          pagination,
          newFilters,
          prevState,
        );
      })
      .catch(error => {
        notification.error({
          message: error.message,
        });
      });
  };

  setProjectTrackerDetails = (
    response,
    updatedKey,
    params,
    pagination,
    prevState,
  ) => {
    if (get(response, 'status')) {
      const query = new URLSearchParams(window.location.search);
      if (query.get('showAll') === 'true') {
        if (response.data.tableData.length === 0) {
          if (this.state.tab !== prevState.tab) {
            this.state.gridApi.showNoRowsOverlay();
          }
        } else {
          this.state.gridApi.hideOverlay();
        }
      } else if (response.data.tableData.docs.length === 0) {
        if (this.state.tab !== prevState.tab) {
          this.state.gridApi.showNoRowsOverlay();
        }
      } else {
        this.state.gridApi.hideOverlay();
      }
      if (updatedKey === 'tracker') {
        if (params) {
          const rowData = get(
            response,
            'data.tableData.docs',
            response.data.tableData,
          );
          const columnData = rowData.map((row, index) => {
            const isFirstRow =
              index === 0 || row.epicName !== rowData[index - 1].epicName;
            if (isFirstRow) {
              let categoryCount = 0;
              for (
                let i = index;
                i < rowData.length && rowData[i].epicName === row.epicName;
                i += 1
              ) {
                categoryCount += 1;
              }
              return { ...row, span: categoryCount };
            }
            return { ...row, span: 0 };
          });
          params.successCallback(
            columnData,
            get(
              response,
              'data.tableData.totalDocs',
              response.data.tableData.length,
            ),
          );
          if (!this.state.firstRender || this.state.firstApiCalled) {
            this.state.gridApi.paginationGoToPage(pagination.current - 1);
          }
        }
        let sprintList = get(response, 'data.filterData.sprint', []);
        sprintList = sprintList.map(sprint => ({
          text: sprint.name,
          value: sprint.name,
        }));
        this.setState(
          prev => ({
            sprintList,
            firstRender: false,
            firstApiCalled: true,
            isListLoading: false,
            projectTracker: {
              ...prev.projectTracker,
              list: get(
                response,
                'data.tableData.docs',
                response.data.tableData,
              ),
              columns: getColumnPropsForTrackerSheet(
                sprintList,
                response.data.epicStatus,
                this.showModal,
                this.state.boardId,
              ),
            },
          }),
          () => {
            this.state.gridApi.refreshHeader();
          },
        );
      } else {
        const rowData = get(
          response,
          'data.tableData.docs',
          response.data.tableData,
        );
        if (params) {
          params.successCallback(
            rowData,
            get(
              response,
              'data.tableData.totalDocs',
              response.data.tableData.length,
            ),
          );
          if (!this.state.firstRender || this.state.firstApiCalled) {
            this.state.gridApi.paginationGoToPage(pagination.current - 1);
          }
        }
        let sprintList = get(response, 'data.filterData.sprint', []);
        sprintList = sprintList.map(sprint => ({
          text: sprint.name,
          value: sprint.name,
        }));
        this.setState(
          prev => ({
            sprintList,
            firstRender: false,
            firstApiCalled: true,
            isListLoading: false,
            sprintMetrics: {
              ...prev.sprintMetrics,
              list: get(
                response,
                'data.tableData.docs',
                response.data.tableData,
              ),
            },
          }),
          () => {
            this.state.gridApi.refreshHeader();
          },
        );
      }
    } else {
      notification.error({ message: get(response, 'message') });
    }
  };

  onTrackerTableChange = (pagination, filters, sorter, params) => {
    setDeepLinkURL({
      projectId: this.state.projectId,
      boardId: this.state.boardId,
      tab: this.state.tab,
      currentPage: pagination.current,
      showAll: this.state.projectTracker.showAll,
      sortKey: sorter.columnKey,
      sortType: GET_SORT_ORDER(sorter && sorter.order),
      ...this.state.projectTracker.filters,
    });

    this.loadProjectTrackerDetails(
      {
        sortType: GET_SORT_ORDER(sorter ? sorter.order : ''),
        sortKey: sorter ? sorter.columnKey : this.state.sortKey,
        pagination,
        params,
        filters,
      },
      'tracker',
    );
  };

  onSprintsTableChange = (pagination, filters, sorter, params, prevState) => {
    const { state } = this;
    this.setState(prev => ({
      sprintMetrics: {
        ...prev.sprintMetrics,
        sortType: GET_SORT_ORDER(sorter && sorter.order),
        sortKey: sorter ? sorter.columnKey : prev.sortKey,
      },
    }));
    setDeepLinkURL({
      projectId: this.state.projectId,
      boardId: this.state.boardId,
      currentPage: pagination.current,
      tab: this.state.tab,
      showAll: this.state.sprintMetrics.showAll,
      sortKey: sorter.columnKey,
      sortType: GET_SORT_ORDER(sorter && sorter.order),
      ...this.state.sprintMetrics.filters,
    });
    this.loadProjectTrackerDetails(
      {
        sortType: GET_SORT_ORDER(sorter ? sorter.order : ''),
        sortKey: sorter ? sorter.columnKey : state.sortKey,
        pagination,
        params,
        filters,
        prevState,
      },
      'sprints',
    );
  };

  onTabChange = tab => {
    const {
      projectTracker,
      sprintMetrics,
      monthlyDeviation,
      projectId,
      boardId,
    } = this.state;
    this.setState({ tab });
    if (tab === 'sprints') {
      setDeepLinkURL({
        projectId,
        boardId,
        tab,
        sortType: sprintMetrics.sortType,
        sortKey: sprintMetrics.sortKey,
        currentPage: sprintMetrics.currentPage,
        showAll: sprintMetrics.showAll,
        ...this.state.sprintMetrics.filters,
      });
    } else if (tab === 'monthly-deviation') {
      setDeepLinkURL({
        projectId,
        boardId,
        tab,
        sortType: monthlyDeviation.sortType,
        sortKey: monthlyDeviation.sortKey,
        currentPage: monthlyDeviation.currentPage,
        showAll: monthlyDeviation.showAll,
        ...this.state.monthlyDeviation.filters,
      });
      this.loadMonthlyDeviationDetails();
    } else {
      setDeepLinkURL({
        projectId,
        boardId,
        tab,
        sortType: projectTracker.sortType,
        sortKey: projectTracker.sortKey,
        currentPage: projectTracker.currentPage,
        showAll: projectTracker.showAll,
        ...this.state.projectTracker.filters,
      });
    }
  };

  onShowAllSwitchToggled = value => {
    const {
      tab,
      projectId,
      boardId,
      projectTracker,
      sprintMetrics,
      monthlyDeviation,
    } = this.state;

    if (tab === 'tracker') {
      setDeepLinkURL({
        projectId,
        boardId,
        sortType: projectTracker.sortType,
        sortKey: projectTracker.sortKey,
        currentPage: projectTracker.currentPage,
        tab: this.state.tab,
        showAll: value,
        ...this.state.projectTracker.filters,
      });
      this.setState(prev => ({
        isListLoading: false,
        projectTracker: {
          ...prev.projectTracker,
          showAll: value,
        },
      }));
    } else if (tab === 'monthly-deviation') {
      setDeepLinkURL({
        projectId,
        boardId,
        sortType: monthlyDeviation.sortType,
        sortKey: monthlyDeviation.sortKey,
        currentPage: monthlyDeviation.currentPage,
        tab: this.state.tab,
        showAll: value,
        ...this.state.monthlyDeviation.filters,
      });
      this.setState(prev => ({
        isListLoading: false,
        monthlyDeviation: {
          ...prev.monthlyDeviation,
          showAll: value,
        },
      }));
    } else {
      setDeepLinkURL({
        projectId,
        boardId,
        sortType: sprintMetrics.sortType,
        sortKey: sprintMetrics.sortKey,
        currentPage: sprintMetrics.currentPage,
        tab: this.state.tab,
        showAll: value,
        ...this.state.sprintMetrics.filters,
      });
      this.setState(prev => ({
        isListLoading: false,
        sprintMetrics: {
          ...prev.sprintMetrics,
          showAll: value,
        },
      }));
    }
  };

  getDefaultSwitchValue = () => {
    const { tab, sprintMetrics, projectTracker, monthlyDeviation } = this.state;
    if (tab === 'sprints') {
      return sprintMetrics.showAll;
    }
    if (tab === 'monthly-deviation') {
      return monthlyDeviation.showAll;
    }
    return projectTracker.showAll;
  };

  createTrackerWorkbook = async trackerData => {
    const ws = XLSX.utils.aoa_to_sheet([[]]);
    const header = TRACKER_SHEET_HEADERS;
    XLSX.utils.sheet_add_json(ws, [TRACKER_SHEET_HEADERS_TO_API_MAPPING], {
      header,
      skipHeader: true,
    });

    trackerData.forEach((trackData, index) => {
      const origin = `A${index + 2}`;
      const rowData = { ...trackData };
      delete rowData._id;
      delete rowData.createdAt;
      delete rowData.storyLink;
      delete rowData.sprintUrl;
      delete rowData.jiraEpicUrl;
      delete rowData.extractedStoryNumber;
      rowData.startDate = rowData.startDate
        ? moment(rowData.startDate).format(PTA_MOMENT_DATE_FORMAT)
        : '';
      rowData.endDate = rowData.endDate
        ? moment(rowData.endDate).format(PTA_MOMENT_DATE_FORMAT)
        : '';
      XLSX.utils.sheet_add_json(ws, [rowData], {
        header,
        origin,
        skipHeader: true,
      });
      ws[`A${index + 2}`].l = {
        Target: trackData.jiraEpicUrl,
      };
      ws[`D${index + 2}`].l = {
        Target: trackData.sprintUrl,
      };
      ws[`E${index + 2}`].l = {
        Target: trackData.storyLink,
      };
    });

    return ws;
  };

  createSprintWorkbook = async sprintData => {
    const ws = XLSX.utils.aoa_to_sheet([[]]);
    const header = SPRINT_SHEET_HEADERS;
    XLSX.utils.sheet_add_json(ws, [SPRINT_SHEET_HEADERS_TO_API_MAPPING], {
      header,
      skipHeader: true,
    });

    sprintData.forEach((trackData, index) => {
      const origin = `A${index + 2}`;
      const rowData = { ...trackData };
      delete rowData.sprintUrl;
      rowData.sprintStartDate = rowData.sprintStartDate
        ? moment(rowData.sprintStartDate).format(PTA_MOMENT_DATE_FORMAT)
        : '';
      rowData.sprintEndDate = rowData.sprintEndDate
        ? moment(rowData.sprintEndDate).format(PTA_MOMENT_DATE_FORMAT)
        : '';
      XLSX.utils.sheet_add_json(ws, [rowData], {
        header,
        origin,
        skipHeader: true,
      });
      ws[`C${index + 2}`].l = {
        Target: trackData.sprintUrl,
      };
    });

    return ws;
  };

  downloadReport = async () => {
    this.setState({ downloadLoading: true });

    const requestURL = `${API_ENDPOINTS.DOWNLOAD_REPORTS}?boardId=${
      this.state.boardId
    }`;
    const response = await request(requestURL, { method: 'GET' });
    const trackerWorkbook = await this.createTrackerWorkbook(
      response.data.projectTracker,
    );
    const sprintWorkbook = await this.createSprintWorkbook(
      response.data.sprintReport,
    );

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, trackerWorkbook, 'Tracker');
    XLSX.utils.book_append_sheet(wb, sprintWorkbook, 'Sprint');
    XLSX.writeFile(wb, `${this.state.selectedProjectName}_tracker_sheet.xlsx`);
    this.setState({ downloadLoading: false });
  };

  getRowClassName = (params, uniqueEpicNames) => {
    const epicIndex = uniqueEpicNames.indexOf(
      params.data && params.data.epicName,
    );
    if (epicIndex % 2 === 0) {
      return 'even-row';
    }
    return 'odd-row';
  };

  getDefaultSorter = sorterParams => {
    const query = new URLSearchParams(window.location.search);
    if (query.get('sortKey') && query.get('sortType')) {
      if (Number(query.get('sortType')) === 1) {
        if (sorterParams && sorterParams && sorterParams.length > 0) {
          return {
            columnKey: sorterParams[0].colId,
            order: sorterParams[0].sort,
          };
        }
        return {
          columnKey: 'createdAt',
          order: 'desc',
        };
      }
      if (sorterParams && sorterParams && sorterParams.length > 0) {
        return {
          columnKey: sorterParams[0].colId,
          order: sorterParams[0].sort,
        };
      }
      return {
        columnKey: 'createdAt',
        order: 'desc',
      };
    }
    return {
      columnKey: 'createdAt',
      order: 'desc',
    };
  };

  componentDidUpdate(prevProps, prevState) {
    const query = new URLSearchParams(window.location.search);
    const gridApiSprintMetricsCondition =
      this.state.gridApi !== prevState.gridApi ||
      this.state.sprintMetrics.showAll !== prevState.sprintMetrics.showAll;
    const projectTrackerBoardIdCondition =
      this.state.projectTracker.showAll !== prevState.projectTracker.showAll ||
      this.state.boardId !== prevState.boardId;
    if (
      gridApiSprintMetricsCondition ||
      projectTrackerBoardIdCondition ||
      this.state.tab !== prevState.tab
    ) {
      const { gridApi } = this.state;
      if (gridApi) {
        const dataSource = {
          getRows: params => {
            // loading
            gridApi.showLoadingOverlay();

            // pagination
            const pageFromUrl = +query.get('currentPage') || 1;
            let pageFromGrid = params.endRow ? params.endRow / 10 : 1;
            const showAll = query.get('showAll') !== 'false';
            if (showAll) {
              pageFromGrid = 1;
            }
            const pagination = {
              pageSize: 10,
              current: !this.state.firstApiCalled ? pageFromUrl : pageFromGrid,
            };

            // filters
            let filters = params.filterModel;
            if (this.state.tab !== prevState.tab) {
              pagination.current = 1;
              const filtersFromState =
                this.state.tab === 'tracker'
                  ? this.state.projectTracker.filters
                  : this.state.sprintMetrics.filters;
              filters = {};
              Object.keys(filtersFromState).forEach(key => {
                filters[key] = {
                  filter: filtersFromState[key],
                  filterType: 'text',
                  type: 'contains',
                };
              });
            }
            if (
              filters &&
              this.state.firstRender &&
              Object.keys(filters).length === 0
            ) {
              if (query.get('epicName')) {
                filters.epicName = {
                  filter: query.get('epicName'),
                  filterType: 'text',
                  type: 'contains',
                };
              }
              if (query.get('storyNumber')) {
                filters.storyNumber = {
                  filter: query.get('storyNumber'),
                  filterType: 'text',
                  type: 'contains',
                };
              }
              if (query.get('status')) {
                filters.status = {
                  filter: query.get('status'),
                  filterType: 'text',
                  type: 'contains',
                };
              }
              if (query.get('sprintName')) {
                filters.sprintName = {
                  filter: query.get('sprintName'),
                  filterType: 'text',
                  type: 'contains',
                };
              }
              if (query.get('')) {
                filters.epicStatus = {
                  filter: query.get('epicStatus'),
                  filterType: 'text',
                  type: 'contains',
                };
              }
            }
            const filterKeys = Object.keys(filters);
            const filtersObj = {};
            filterKeys.forEach(filter => {
              filtersObj[filter] = filters[filter].filter;
            });
            this.state.gridApi.setFilterModel(filters);

            // sorting
            if (query.get('sortKey')) {
              if (params && params.sortModel && params.sortModel.length === 0) {
                const columnState = {
                  state: this.state.firstRender
                    ? [
                        {
                          colId: query.get('sortKey'),
                          sort:
                            Number(query.get('sortType')) === 1
                              ? 'asc'
                              : 'desc',
                        },
                      ]
                    : [],
                };
                this.setState(
                  {
                    firstRender: false,
                  },
                  () => {
                    this.state.columnApi.applyColumnState(columnState);
                  },
                );
              }
            }
            let sorterParams = this.getDefaultSorter(params.sortModel);
            if (params && params.sortModel && params.sortModel.length > 0) {
              if (params.sortModel[0].sort === 'asc') {
                sorterParams = {
                  columnKey: params.sortModel[0].colId,
                  order: 'ascend',
                };
                this.setState({
                  sortType: 1,
                  sortKey: params.sortModel[0].colId,
                });
              } else if (params.sortModel[0].sort === 'desc') {
                sorterParams = {
                  columnKey: params.sortModel[0].colId,
                  order: 'descend',
                };
                this.setState({
                  sortType: -1,
                  sortKey: params.sortModel[0].colId,
                });
              } else {
                sorterParams = {
                  columnKey: 'createdAt',
                  order: 'descend',
                };
              }
            }

            // set state + api call
            if (this.state.tab === 'sprints') {
              this.setState(
                prev => ({
                  sprintMetrics: {
                    ...prev.sprintMetrics,
                    currentPage: pagination.current,
                    sortKey: sorterParams.columnKey,
                    sortType: sorterParams.order,
                    filters: filtersObj,
                    showAll,
                  },
                }),
                () => {
                  this.onSprintsTableChange(
                    pagination,
                    filters,
                    sorterParams,
                    params,
                    prevState,
                  );
                },
              );
            } else {
              this.setState(
                prev => ({
                  projectTracker: {
                    ...prev.projectTracker,
                    currentPage: pagination.current,
                    sortKey: sorterParams.columnKey,
                    sortType: sorterParams.order,
                    filters: filtersObj,
                    showAll,
                  },
                }),
                () => {
                  this.onTrackerTableChange(
                    pagination,
                    filters,
                    sorterParams,
                    params,
                    showAll,
                    prevState,
                  );
                },
              );
            }
          },
        };
        gridApi.setDatasource(dataSource);
      }
    }
  }

  onGridReady = params => {
    this.setState({ gridApi: params.api, columnApi: params.columnApi });
  };

  handleTriggerCron = async () => {
    // call trigger cron API
    await this.triggerCronAPI(this.state.projectId);
  };

  triggerCronAPI = async projectId => {
    const URL = `${API_ENDPOINTS.TRIGGER_CRON}?projectId=${projectId}`;
    this.setState(prev => ({
      ...prev,
      triggerLoading: true,
      isCronRunning: true,
      inProgressProjectName: prev.selectedProjectName,
    }));
    request(URL, {
      method: 'GET',
    })
      .then(res => {
        // call get projectList API to update the boardIds
        if (res.status === 1) {
          this.getProjectList({ projectId, boardId: undefined });
        } else {
          notification.error({
            message: <FormattedMessage {...messages.failedToLoad} />,
            description: 'Cron did not triggered successfully',
          });
        }
      })
      .catch(error => {
        notification.error({
          message: <FormattedMessage {...messages.failedToLoad} />,
          description: error.message,
        });
        this.setState({ isLoading: false });
      })
      .finally(() => {
        this.setState({
          triggerLoading: false,
          isCronRunning: false,
          inProgressProjectName: '',
        });
      });
  };

  checkCronStatus = async () => {
    const URL = `${API_ENDPOINTS.CRON_STATUS}`;
    request(URL, {
      method: 'GET',
    })
      .then(res => {
        // call get projectList API to update the boardIds
        if (res.status === 1) {
          this.setState({
            isCronRunning: res.data.isCronRunning,
          });
        } else {
          notification.error({
            message: <FormattedMessage {...messages.failedToLoad} />,
            description: 'Unable to check cron status',
          });
        }
      })
      .catch(error => {
        notification.error({
          message: <FormattedMessage {...messages.failedToLoad} />,
          description: error.message,
        });
        this.setState({
          isCronRunning: false,
        });
      });
  };

  loadMonthlyDeviationDetails = () => {
    this.setState({ isListLoading: true });
    const { boardId, projectId } = this.state;
    const requestURL = `${
      API_ENDPOINTS.GET_MONTHLY_DEVIATION
    }?boardId=${boardId}&projectId=${projectId}`;

    request(requestURL, { method: 'GET' })
      .then(response => {
        if (response.status === 1) {
          this.setState(prev => ({
            monthlyDeviation: {
              ...prev.monthlyDeviation,
              list: response.data.docs || [], // Make sure you're getting the correct data path
              showAll: true,
            },
            isListLoading: false,
          }));
        } else {
          notification.error({
            message: get(response, 'message'),
          });
        }
      })
      .catch(error => {
        notification.error({
          message: error.message,
        });
        this.setState({ isListLoading: false });
      });
  };

  render() {
    const {
      selectedProjectName,
      projectTracker,
      sprintMetrics,
      monthlyDeviation,
      isLoading,
      projectList,
      projectId,
      boardList,
      boardId,
      showCardsAndTabs,
      tab,
      downloadLoading,
      sprintList,
      statusCardsData,
      triggerLoading,
      isCronRunning,
      inProgressProjectName,
    } = this.state;
    const uniqueEpicNames = [];
    if (projectTracker.list) {
      projectTracker.list.forEach(data => {
        if (!uniqueEpicNames.includes(data.epicName)) {
          uniqueEpicNames.push(data.epicName);
        }
      });
    }

    return (
      <StyledProjectTrackerMainDiv>
        <Helmet>
          <title>Project Tracker</title>
          <meta name="description" content="Project Tracker" />
        </Helmet>
        <div className="projectTrackerHeader">
          <PageHeader
            title={`Project Tracker ${
              selectedProjectName === '' ? '' : `- ${selectedProjectName}`
            }`}
            className="site-page-header"
            extra={
              isLoading
                ? []
                : [
                    <Select
                      key="1"
                      style={{ width: '150px' }}
                      placeholder="Select Project"
                      options={projectList}
                      onChange={this.filterByProject}
                      data-testid="select-project"
                      allowClear
                      showSearch
                      filterOption={(input, option) =>
                        option &&
                        option.label
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      }
                      defaultValue={projectId || undefined}
                    />,
                    <Select
                      key="2"
                      style={{ width: '150px' }}
                      placeholder="Select board"
                      options={boardList}
                      onChange={this.filterByBoard}
                      data-testid="select-board"
                      allowClear
                      showSearch
                      filterOption={(input, option) =>
                        option &&
                        option.label
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      }
                      value={boardId}
                      defaultValue={boardId || undefined}
                      disabled={!projectId}
                    />,
                    <Button
                      onClick={this.handleTriggerCron}
                      loading={triggerLoading}
                      className="trigger-btn"
                      data-testid="trigger-btn"
                      disabled={isCronRunning}
                    >
                      Sync JIRA
                    </Button>,
                  ]
            }
          />

          {isCronRunning ? (
            <div
              className="cron-inprogress"
              data-testid="cron-inprogress-message"
            >
              <span>
                <InfoCircleOutlined />
                &nbsp;
                {getInProgressMessage(inProgressProjectName)}
              </span>
            </div>
          ) : null}
        </div>
        {showCardsAndTabs && (
          <StatusCard
            selectedProjectName={this.state.selectedProjectName}
            cardData={statusCardsData}
            loading={this.state.cardsLoading}
          />
        )}
        {showCardsAndTabs && (
          <div className="tabs-table">
            <Tabs
              defaultActiveKey={tab}
              destroyInactiveTabPane
              activeKey={tab}
              onChange={this.onTabChange}
              tabBarExtraContent={
                <div className="tab-extra-content">
                  <Switch
                    checkedChildren="Show With Pages"
                    unCheckedChildren="Show All"
                    defaultChecked={this.getDefaultSwitchValue()}
                    checked={this.getDefaultSwitchValue()}
                    onClick={this.onShowAllSwitchToggled}
                  />
                  <Button
                    className="download-btn"
                    data-testid="download-btn"
                    onClick={this.downloadReport}
                    loading={downloadLoading}
                  >
                    Download Report
                  </Button>
                </div>
              }
            >
              <Tabs.TabPane key="tracker" tab={<div>Tracker</div>}>
                <>
                  {projectTracker.showAll ? (
                    <div className="ag-theme-quartz" style={{ height: 600 }}>
                      <div />
                      <AgGridReact
                        columnDefs={projectTracker.columns}
                        getRowClass={params =>
                          this.getRowClassName(params, uniqueEpicNames)
                        }
                        suppressRowTransform
                        defaultColDef={{
                          sortable: false,
                          suppressMenu: true,
                          floatingFilterComponentParams: {
                            suppressFilterButton: true,
                          },
                        }}
                        rowModelType="infinite"
                        maxBlocksInCache={1}
                        cacheBlockSize={
                          projectTracker.list && projectTracker.list.length
                        }
                        onGridReady={this.onGridReady}
                        suppressDragLeaveHidesColumns
                        overlayNoRowsTemplate="No Epics found"
                      />
                    </div>
                  ) : (
                    <div className="ag-theme-quartz" style={{ height: 600 }}>
                      <AgGridReact
                        columnDefs={projectTracker.columns}
                        suppressRowTransform
                        defaultColDef={{
                          sortable: false,
                          suppressMenu: true,
                          floatingFilterComponentParams: {
                            suppressFilterButton: true,
                          },
                        }}
                        getRowClass={params =>
                          this.getRowClassName(params, uniqueEpicNames)
                        }
                        rowModelType="infinite"
                        pagination
                        cacheBlockSize={10}
                        maxBlocksInCache={1}
                        paginationPageSize={10}
                        onGridReady={this.onGridReady}
                        suppressDragLeaveHidesColumns
                        overlayNoRowsTemplate="No Epics found"
                      />
                    </div>
                  )}
                </>
              </Tabs.TabPane>
              <Tabs.TabPane key="sprints" tab={<div>Sprints</div>}>
                <>
                  {sprintMetrics.showAll ? (
                    <div className="ag-theme-quartz" style={{ height: 500 }}>
                      <div />
                      <AgGridReact
                        columnDefs={getColumnPropsForSprintSheet(sprintList)}
                        rowModelType="infinite"
                        maxBlocksInCache={1}
                        cacheBlockSize={
                          sprintMetrics.list && sprintMetrics.list.length
                        }
                        onGridReady={this.onGridReady}
                        autoSizeStrategy={{
                          type: 'fitGridWidth',
                          defaultMinWidth: 100,
                        }}
                        defaultColDef={{
                          sortable: true,
                          suppressMenu: true,
                          floatingFilterComponentParams: {
                            suppressFilterButton: true,
                          },
                        }}
                        suppressDragLeaveHidesColumns
                        overlayNoRowsTemplate="No Sprints found"
                      />
                    </div>
                  ) : (
                    <div className="ag-theme-quartz" style={{ height: 500 }}>
                      <AgGridReact
                        columnDefs={getColumnPropsForSprintSheet(sprintList)}
                        rowModelType="infinite"
                        pagination
                        cacheBlockSize={10}
                        maxBlocksInCache={1}
                        paginationPageSize={10}
                        onGridReady={this.onGridReady}
                        suppressDragLeaveHidesColumns
                        autoSizeStrategy={{
                          type: 'fitGridWidth',
                          defaultMinWidth: 100,
                        }}
                        defaultColDef={{
                          sortable: true,
                          suppressMenu: true,
                          floatingFilterComponentParams: {
                            suppressFilterButton: true,
                          },
                        }}
                        overlayNoRowsTemplate="No Sprints found"
                      />
                    </div>
                  )}
                </>
              </Tabs.TabPane>
              <Tabs.TabPane
                key="monthly-deviation"
                tab={<div>Monthly Deviation</div>}
              >
                <>
                  <div className="ag-theme-quartz" style={{ height: 500 }}>
                    <AgGridReact
                      columnDefs={monthlyDeviationColumns()}
                      rowData={monthlyDeviation.list}
                      rowModelType="clientSide"
                      pagination={!monthlyDeviation.showAll}
                      paginationPageSize={10}
                      cacheBlockSize={10}
                      onGridReady={this.onGridReady}
                      suppressDragLeaveHidesColumns
                      autoSizeStrategy={{
                        type: 'fitGridWidth',
                        defaultMinWidth: 100,
                      }}
                      defaultColDef={{
                        sortable: true,
                        suppressMenu: true,
                        resizable: true,
                        floatingFilterComponentParams: {
                          suppressFilterButton: true,
                        },
                      }}
                      overlayNoRowsTemplate="No monthly deviation data found"
                    />
                  </div>
                </>
              </Tabs.TabPane>
            </Tabs>
          </div>
        )}
        <ModalComponent
          title={this.state.title}
          isVisible={this.state.modalVisible}
          content={this.state.modalContent}
          closeModal={this.closeModal}
          style={{ maxHeight: '100px', overflowY: 'scroll' }}
        />
      </StyledProjectTrackerMainDiv>
    );
  }
}

export default ProjectTracker;
